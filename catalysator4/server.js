require('dotenv').config();
const express = require('express');
const session = require('express-session');
const { google } = require('googleapis');

const app = express();
app.use(session({ secret: 'TVOJ_SECRET', resave: false, saveUninitialized: true }));

// OAuth2 klient
const oauth2 = new google.auth.OAuth2(
  process.env.CLIENT_ID,
  process.env.CLIENT_SECRET,
  'http://localhost:3000/auth/callback'
);

// Endpoint pre callback
app.get('/auth/callback', async (req, res) => {
  const code = req.query.code;
  const { tokens } = await oauth2.getToken(code);
  req.session.tokens = tokens;                // ulož tokeny do session
  res.redirect('/');                          // späť na hlavnú
});

// Jednoduchý API endpoint na overenie tokenu a volanie Gmail API
app.get('/api/profile', async (req, res) => {
  if (!req.session.tokens) return res.status(401).send('Nie si prihl<PERSON>ený');
  oauth2.setCredentials(req.session.tokens);

  const gmail = google.gmail({ version: 'v1', auth: oauth2 });
  const profile = await gmail.users.getProfile({ userId: 'me' });
  res.json(profile.data);
});

app.listen(3000, () => console.log('Beží na http://localhost:3000'));
